#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局交易对配置模块
统一管理整个回购程序的交易对配置，包括行情、资产、交易等
"""

import os
import json
import logging
from typing import Dict, Tuple, Optional
from dataclasses import dataclass

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class SymbolConfig:
    """交易对配置数据类"""
    symbol: str                    # 交易对，如 "bybusdt"
    base_currency: str            # 基础货币，如 "byb"
    quote_currency: str           # 计价货币，如 "usdt"
    min_order_size: float         # 最小订单量
    max_order_size: float         # 最大订单量
    price_precision: int          # 价格精度
    size_precision: int           # 数量精度
    default_price: float          # 默认价格（用于价格获取失败时）
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保交易对是小写
        self.symbol = self.symbol.lower()
        self.base_currency = self.base_currency.lower()
        self.quote_currency = self.quote_currency.lower()


class GlobalSymbolManager:
    """全局交易对管理器"""
    
    def __init__(self, config_file: str = None):
        """初始化管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        self.config_file = config_file or os.path.join(
            os.path.dirname(__file__), 'symbol_config.json'
        )
        self._current_symbol_config: Optional[SymbolConfig] = None
        self._symbol_configs: Dict[str, SymbolConfig] = {}
        
        # 加载配置
        self._load_configs()
        
        # 如果没有配置文件，创建默认配置
        if not os.path.exists(self.config_file):
            self._create_default_config()
            self._load_configs()
    
    def _create_default_config(self):
        """创建默认配置文件"""
        default_configs = {
            "current_symbol": "bybusdt",
            "symbols": {
                "bybusdt": {
                    "symbol": "bybusdt",
                    "base_currency": "byb",
                    "quote_currency": "usdt",
                    "min_order_size": 100.0,
                    "max_order_size": 800.0,
                    "price_precision": 6,
                    "size_precision": 2,
                    "default_price": 0.1
                },
                "manausdt": {
                    "symbol": "manausdt",
                    "base_currency": "mana",
                    "quote_currency": "usdt",
                    "min_order_size": 50.0,
                    "max_order_size": 500.0,
                    "price_precision": 4,
                    "size_precision": 2,
                    "default_price": 0.5
                },
                "btcusdt": {
                    "symbol": "btcusdt",
                    "base_currency": "btc",
                    "quote_currency": "usdt",
                    "min_order_size": 0.001,
                    "max_order_size": 0.01,
                    "price_precision": 2,
                    "size_precision": 6,
                    "default_price": 50000.0
                }
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_configs, f, indent=2, ensure_ascii=False)
            logger.info(f"创建默认配置文件: {self.config_file}")
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {str(e)}")
    
    def _load_configs(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_file):
                logger.warning(f"配置文件不存在: {self.config_file}")
                return
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载所有交易对配置
            self._symbol_configs = {}
            for symbol, config in config_data.get('symbols', {}).items():
                self._symbol_configs[symbol] = SymbolConfig(**config)
            
            # 设置当前交易对
            current_symbol = config_data.get('current_symbol', 'bybusdt')
            if current_symbol in self._symbol_configs:
                self._current_symbol_config = self._symbol_configs[current_symbol]
                logger.info(f"加载配置成功，当前交易对: {current_symbol}")
            else:
                logger.error(f"当前交易对 {current_symbol} 不存在于配置中")
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
    
    def _save_configs(self):
        """保存配置文件"""
        try:
            config_data = {
                "current_symbol": self._current_symbol_config.symbol if self._current_symbol_config else "bybusdt",
                "symbols": {}
            }
            
            for symbol, config in self._symbol_configs.items():
                config_data["symbols"][symbol] = {
                    "symbol": config.symbol,
                    "base_currency": config.base_currency,
                    "quote_currency": config.quote_currency,
                    "min_order_size": config.min_order_size,
                    "max_order_size": config.max_order_size,
                    "price_precision": config.price_precision,
                    "size_precision": config.size_precision,
                    "default_price": config.default_price
                }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置文件保存成功: {self.config_file}")
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
    
    def get_current_symbol(self) -> str:
        """获取当前交易对"""
        return self._current_symbol_config.symbol if self._current_symbol_config else "bybusdt"
    
    def get_current_config(self) -> SymbolConfig:
        """获取当前交易对配置"""
        if not self._current_symbol_config:
            raise ValueError("当前交易对配置未设置")
        return self._current_symbol_config
    
    def get_base_currency(self) -> str:
        """获取基础货币"""
        return self.get_current_config().base_currency
    
    def get_quote_currency(self) -> str:
        """获取计价货币"""
        return self.get_current_config().quote_currency
    
    def get_currency_pair(self) -> Tuple[str, str]:
        """获取货币对
        
        Returns:
            Tuple[str, str]: (基础货币, 计价货币)
        """
        config = self.get_current_config()
        return config.base_currency, config.quote_currency
    
    def get_min_order_size(self) -> float:
        """获取最小订单量"""
        return self.get_current_config().min_order_size
    
    def get_max_order_size(self) -> float:
        """获取最大订单量"""
        return self.get_current_config().max_order_size
    
    def get_price_precision(self) -> int:
        """获取价格精度"""
        return self.get_current_config().price_precision
    
    def get_size_precision(self) -> int:
        """获取数量精度"""
        return self.get_current_config().size_precision
    
    def get_default_price(self) -> float:
        """获取默认价格"""
        return self.get_current_config().default_price
    
    def set_current_symbol(self, symbol: str) -> bool:
        """设置当前交易对
        
        Args:
            symbol: 交易对名称
            
        Returns:
            bool: 是否设置成功
        """
        symbol = symbol.lower()
        if symbol not in self._symbol_configs:
            logger.error(f"交易对 {symbol} 不存在于配置中")
            return False
        
        self._current_symbol_config = self._symbol_configs[symbol]
        self._save_configs()
        logger.info(f"当前交易对已切换为: {symbol}")
        return True
    
    def add_symbol_config(self, config: SymbolConfig) -> bool:
        """添加交易对配置
        
        Args:
            config: 交易对配置
            
        Returns:
            bool: 是否添加成功
        """
        try:
            self._symbol_configs[config.symbol] = config
            self._save_configs()
            logger.info(f"添加交易对配置成功: {config.symbol}")
            return True
        except Exception as e:
            logger.error(f"添加交易对配置失败: {str(e)}")
            return False
    
    def get_available_symbols(self) -> list:
        """获取所有可用的交易对"""
        return list(self._symbol_configs.keys())
    
    def get_symbol_config(self, symbol: str) -> Optional[SymbolConfig]:
        """获取指定交易对的配置
        
        Args:
            symbol: 交易对名称
            
        Returns:
            SymbolConfig: 交易对配置，如果不存在返回None
        """
        return self._symbol_configs.get(symbol.lower())


# 全局实例
_global_symbol_manager = None

def get_symbol_manager() -> GlobalSymbolManager:
    """获取全局交易对管理器实例"""
    global _global_symbol_manager
    if _global_symbol_manager is None:
        _global_symbol_manager = GlobalSymbolManager()
    return _global_symbol_manager

# 便捷函数
def get_current_symbol() -> str:
    """获取当前交易对"""
    return get_symbol_manager().get_current_symbol()

def get_base_currency() -> str:
    """获取基础货币"""
    return get_symbol_manager().get_base_currency()

def get_quote_currency() -> str:
    """获取计价货币"""
    return get_symbol_manager().get_quote_currency()

def get_currency_pair() -> Tuple[str, str]:
    """获取货币对"""
    return get_symbol_manager().get_currency_pair()

def set_current_symbol(symbol: str) -> bool:
    """设置当前交易对"""
    return get_symbol_manager().set_current_symbol(symbol)

def get_current_config() -> SymbolConfig:
    """获取当前交易对配置"""
    return get_symbol_manager().get_current_config()

if __name__ == "__main__":
    # 测试代码
    print("全局交易对配置模块测试")
    print("=" * 50)
    
    manager = get_symbol_manager()
    print(f"当前交易对: {get_current_symbol()}")
    print(f"基础货币: {get_base_currency()}")
    print(f"计价货币: {get_quote_currency()}")
    print(f"可用交易对: {manager.get_available_symbols()}")
    
    # 测试切换交易对
    print("\n测试切换交易对...")
    if set_current_symbol("manausdt"):
        print(f"切换后交易对: {get_current_symbol()}")
        print(f"基础货币: {get_base_currency()}")
        print(f"计价货币: {get_quote_currency()}")
