#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易对管理工具
用于管理和切换回购系统的交易对配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from global_symbol_config import get_symbol_manager, get_current_symbol, set_current_symbol, SymbolConfig
import argparse
import json

def show_current_config():
    """显示当前交易对配置"""
    manager = get_symbol_manager()
    current_config = manager.get_current_config()
    
    print("=" * 60)
    print("当前交易对配置")
    print("=" * 60)
    print(f"交易对: {current_config.symbol}")
    print(f"基础货币: {current_config.base_currency}")
    print(f"计价货币: {current_config.quote_currency}")
    print(f"最小订单量: {current_config.min_order_size}")
    print(f"最大订单量: {current_config.max_order_size}")
    print(f"价格精度: {current_config.price_precision}")
    print(f"数量精度: {current_config.size_precision}")
    print(f"默认价格: {current_config.default_price}")
    print("=" * 60)

def list_available_symbols():
    """列出所有可用的交易对"""
    manager = get_symbol_manager()
    symbols = manager.get_available_symbols()
    
    print("=" * 60)
    print("可用交易对列表")
    print("=" * 60)
    
    current_symbol = get_current_symbol()
    
    for symbol in symbols:
        config = manager.get_symbol_config(symbol)
        if config:
            status = " (当前)" if symbol == current_symbol else ""
            print(f"{symbol.upper():<12} {config.base_currency.upper()}/{config.quote_currency.upper():<8} "
                  f"默认价格: {config.default_price:<10} {status}")
    
    print("=" * 60)

def switch_symbol(symbol: str):
    """切换交易对"""
    print(f"正在切换到交易对: {symbol}")
    
    if set_current_symbol(symbol):
        print(f"✅ 成功切换到交易对: {symbol.upper()}")
        show_current_config()
    else:
        print(f"❌ 切换失败: 交易对 {symbol} 不存在")
        print("可用的交易对:")
        list_available_symbols()

def add_symbol_config():
    """交互式添加新的交易对配置"""
    print("=" * 60)
    print("添加新的交易对配置")
    print("=" * 60)
    
    try:
        symbol = input("交易对名称 (如 ethusdt): ").strip().lower()
        if not symbol:
            print("❌ 交易对名称不能为空")
            return
        
        # 自动解析基础货币和计价货币
        if symbol.endswith('usdt'):
            base_currency = symbol[:-4]
            quote_currency = 'usdt'
        elif symbol.endswith('btc'):
            base_currency = symbol[:-3]
            quote_currency = 'btc'
        elif symbol.endswith('eth'):
            base_currency = symbol[:-3]
            quote_currency = 'eth'
        else:
            base_currency = input("基础货币 (如 eth): ").strip().lower()
            quote_currency = input("计价货币 (如 usdt): ").strip().lower()
        
        print(f"解析结果: {base_currency.upper()}/{quote_currency.upper()}")
        
        min_order_size = float(input(f"最小订单量 (默认 1.0): ") or "1.0")
        max_order_size = float(input(f"最大订单量 (默认 1000.0): ") or "1000.0")
        price_precision = int(input(f"价格精度 (默认 6): ") or "6")
        size_precision = int(input(f"数量精度 (默认 2): ") or "2")
        default_price = float(input(f"默认价格 (默认 1.0): ") or "1.0")
        
        # 创建配置
        config = SymbolConfig(
            symbol=symbol,
            base_currency=base_currency,
            quote_currency=quote_currency,
            min_order_size=min_order_size,
            max_order_size=max_order_size,
            price_precision=price_precision,
            size_precision=size_precision,
            default_price=default_price
        )
        
        # 添加到管理器
        manager = get_symbol_manager()
        if manager.add_symbol_config(config):
            print(f"✅ 成功添加交易对配置: {symbol.upper()}")
            
            # 询问是否切换到新配置
            switch = input("是否切换到新的交易对? (y/N): ").strip().lower()
            if switch == 'y':
                switch_symbol(symbol)
        else:
            print(f"❌ 添加交易对配置失败")
            
    except ValueError as e:
        print(f"❌ 输入格式错误: {str(e)}")
    except Exception as e:
        print(f"❌ 添加配置失败: {str(e)}")

def export_config():
    """导出配置到文件"""
    manager = get_symbol_manager()
    config_data = {
        "current_symbol": get_current_symbol(),
        "symbols": {}
    }
    
    for symbol in manager.get_available_symbols():
        config = manager.get_symbol_config(symbol)
        if config:
            config_data["symbols"][symbol] = {
                "symbol": config.symbol,
                "base_currency": config.base_currency,
                "quote_currency": config.quote_currency,
                "min_order_size": config.min_order_size,
                "max_order_size": config.max_order_size,
                "price_precision": config.price_precision,
                "size_precision": config.size_precision,
                "default_price": config.default_price
            }
    
    filename = f"symbol_config_export_{get_current_symbol()}.json"
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置已导出到: {filename}")

def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='交易对管理工具')
    parser.add_argument('action', nargs='?', choices=['show', 'list', 'switch', 'add', 'export'], 
                       help='操作类型')
    parser.add_argument('symbol', nargs='?', help='交易对名称 (用于switch操作)')
    
    args = parser.parse_args()
    
    if not args.action:
        # 交互式菜单
        while True:
            print("\n" + "=" * 60)
            print("交易对管理工具")
            print("=" * 60)
            print("1. 显示当前配置")
            print("2. 列出所有交易对")
            print("3. 切换交易对")
            print("4. 添加新交易对")
            print("5. 导出配置")
            print("0. 退出")
            print("=" * 60)
            
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("👋 退出程序")
                break
            elif choice == '1':
                show_current_config()
            elif choice == '2':
                list_available_symbols()
            elif choice == '3':
                list_available_symbols()
                symbol = input("\n请输入要切换的交易对名称: ").strip()
                if symbol:
                    switch_symbol(symbol)
            elif choice == '4':
                add_symbol_config()
            elif choice == '5':
                export_config()
            else:
                print("❌ 无效选择，请输入0-5之间的数字")
            
            input("\n按回车键继续...")
    
    else:
        # 命令行模式
        if args.action == 'show':
            show_current_config()
        elif args.action == 'list':
            list_available_symbols()
        elif args.action == 'switch':
            if not args.symbol:
                print("❌ 请指定要切换的交易对名称")
                list_available_symbols()
            else:
                switch_symbol(args.symbol)
        elif args.action == 'add':
            add_symbol_config()
        elif args.action == 'export':
            export_config()

if __name__ == "__main__":
    main()
