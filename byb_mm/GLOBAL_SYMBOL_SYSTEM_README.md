# 全局交易对配置系统

## 概述

全局交易对配置系统是一个统一管理整个回购程序交易对的解决方案，支持动态切换不同的交易对，包括行情获取、资产查询、交易执行等所有相关操作。

## 核心特性

- ✅ **统一配置管理**: 所有交易对配置集中管理，避免硬编码
- ✅ **动态切换**: 支持运行时切换交易对，无需重启程序
- ✅ **多交易对支持**: 预置BYB/USDT、MANA/USDT、BTC/USDT等配置
- ✅ **自动适配**: 所有模块自动适配当前选择的交易对
- ✅ **配置持久化**: 配置自动保存到JSON文件
- ✅ **易于扩展**: 支持添加新的交易对配置

## 文件结构

```
byb_mm/
├── global_symbol_config.py      # 核心配置模块
├── symbol_manager.py            # 交易对管理工具
├── test_global_symbol.py        # 功能测试脚本
├── symbol_config.json           # 配置文件（自动生成）
├── byb_order_algorithm.py       # 修改后的下单算法（支持动态交易对）
├── byb_buy_back.py             # 修改后的回购系统（支持动态交易对）
└── GLOBAL_SYMBOL_SYSTEM_README.md # 说明文档
```

## 快速开始

### 1. 查看当前配置

```bash
python symbol_manager.py show
```

### 2. 列出所有可用交易对

```bash
python symbol_manager.py list
```

### 3. 切换交易对

```bash
# 切换到BYB/USDT
python symbol_manager.py switch bybusdt

# 切换到MANA/USDT
python symbol_manager.py switch manausdt

# 切换到BTC/USDT
python symbol_manager.py switch btcusdt
```

### 4. 交互式管理

```bash
python symbol_manager.py
```

## 配置说明

### 默认交易对配置

系统预置了以下交易对配置：

| 交易对 | 基础货币 | 计价货币 | 最小订单量 | 最大订单量 | 默认价格 |
|--------|----------|----------|------------|------------|----------|
| bybusdt | byb | usdt | 100.0 | 800.0 | 0.1 |
| manausdt | mana | usdt | 50.0 | 500.0 | 0.5 |
| btcusdt | btc | usdt | 0.001 | 0.01 | 50000.0 |

### 配置文件格式

```json
{
  "current_symbol": "bybusdt",
  "symbols": {
    "bybusdt": {
      "symbol": "bybusdt",
      "base_currency": "byb",
      "quote_currency": "usdt",
      "min_order_size": 100.0,
      "max_order_size": 800.0,
      "price_precision": 6,
      "size_precision": 2,
      "default_price": 0.1
    }
  }
}
```

## 编程接口

### 基本使用

```python
from global_symbol_config import (
    get_current_symbol, get_base_currency, get_quote_currency,
    set_current_symbol, get_current_config
)

# 获取当前交易对信息
symbol = get_current_symbol()        # "bybusdt"
base = get_base_currency()           # "byb"
quote = get_quote_currency()         # "usdt"

# 获取完整配置
config = get_current_config()
print(f"最小订单量: {config.min_order_size}")
print(f"最大订单量: {config.max_order_size}")

# 切换交易对
set_current_symbol("manausdt")
```

### 高级使用

```python
from global_symbol_config import get_symbol_manager, SymbolConfig

# 获取管理器实例
manager = get_symbol_manager()

# 添加新的交易对配置
new_config = SymbolConfig(
    symbol="ethusdt",
    base_currency="eth",
    quote_currency="usdt",
    min_order_size=0.01,
    max_order_size=1.0,
    price_precision=2,
    size_precision=4,
    default_price=3000.0
)
manager.add_symbol_config(new_config)

# 获取所有可用交易对
symbols = manager.get_available_symbols()
```

## 集成说明

### 下单算法集成

修改后的 `byb_order_algorithm.py` 自动适配当前交易对：

```python
# 自动获取当前交易对配置
trading_config = get_trading_config()
current_price = algorithm.get_current_price()  # 自动适配当前交易对

# 日志信息自动显示正确的货币单位
logging.info(f"执行金额: {amount:.2f} {get_base_currency().upper()}")
```

### 回购系统集成

修改后的 `byb_buy_back.py` 支持动态交易对：

```python
# 监控配置自动适配
monitor_config = get_monitor_config()
base_currency = get_base_currency()
quote_currency = get_quote_currency()

# 余额检查自动适配
quote_balance = balances.get(quote_currency, {}).get('total', 0)
```

## 使用场景

### 1. 多币种回购

```bash
# 设置BYB回购
python symbol_manager.py switch bybusdt
python byb_buy_back.py

# 设置MANA回购
python symbol_manager.py switch manausdt
python byb_buy_back.py
```

### 2. 测试不同交易对

```bash
# 测试BTC配置
python symbol_manager.py switch btcusdt
python test_global_symbol.py
```

### 3. 批量操作

```python
from global_symbol_config import get_symbol_manager, set_current_symbol

manager = get_symbol_manager()
for symbol in manager.get_available_symbols():
    print(f"切换到 {symbol}")
    set_current_symbol(symbol)
    # 执行相关操作
```

## 添加新交易对

### 方法1: 使用管理工具

```bash
python symbol_manager.py add
```

按提示输入交易对信息。

### 方法2: 编程方式

```python
from global_symbol_config import get_symbol_manager, SymbolConfig

config = SymbolConfig(
    symbol="dogeusdt",
    base_currency="doge",
    quote_currency="usdt",
    min_order_size=1000.0,
    max_order_size=10000.0,
    price_precision=6,
    size_precision=0,
    default_price=0.1
)

manager = get_symbol_manager()
manager.add_symbol_config(config)
```

## 测试验证

运行完整测试套件：

```bash
python test_global_symbol.py
```

测试内容包括：
- 基本功能测试
- 交易对切换测试
- 算法模块集成测试
- 回购系统集成测试
- 多交易对场景测试

## 注意事项

1. **配置文件**: 首次运行会自动创建 `symbol_config.json` 配置文件
2. **权限要求**: 确保程序有读写配置文件的权限
3. **网络连接**: 价格获取功能需要网络连接
4. **API限制**: 不同交易对可能有不同的API限制
5. **精度设置**: 确保价格和数量精度符合交易所要求

## 故障排除

### 配置文件丢失

如果配置文件丢失，系统会自动重新创建默认配置。

### 切换失败

```bash
# 检查可用交易对
python symbol_manager.py list

# 确认交易对名称正确
python symbol_manager.py switch <正确的交易对名称>
```

### 集成问题

```bash
# 运行测试检查集成状态
python test_global_symbol.py
```

## 更新日志

### v1.0.0 (2025-07-08)
- 初始版本发布
- 实现全局交易对配置管理
- 支持BYB、MANA、BTC三个交易对
- 完成算法和回购系统集成
- 提供完整的管理工具和测试套件

## 总结

全局交易对配置系统成功实现了：

1. **统一管理**: 所有交易对配置集中管理，消除硬编码
2. **动态切换**: 支持运行时切换，提高系统灵活性
3. **自动适配**: 所有模块自动适配当前交易对
4. **易于扩展**: 简单添加新的交易对支持
5. **完整测试**: 提供全面的测试验证

通过这个系统，回购程序现在可以灵活支持多种交易对，大大提高了系统的可用性和扩展性。
