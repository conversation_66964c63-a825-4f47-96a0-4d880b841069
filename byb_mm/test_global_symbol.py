#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局交易对配置测试脚本
验证交易对切换功能和相关模块的集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from global_symbol_config import (
    get_symbol_manager, get_current_symbol, get_base_currency, 
    get_quote_currency, set_current_symbol, get_current_config
)
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试基本功能")
    print("=" * 60)
    
    try:
        # 测试获取当前配置
        current_symbol = get_current_symbol()
        base_currency = get_base_currency()
        quote_currency = get_quote_currency()
        config = get_current_config()
        
        print(f"✅ 当前交易对: {current_symbol}")
        print(f"✅ 基础货币: {base_currency}")
        print(f"✅ 计价货币: {quote_currency}")
        print(f"✅ 最小订单量: {config.min_order_size}")
        print(f"✅ 最大订单量: {config.max_order_size}")
        print(f"✅ 默认价格: {config.default_price}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {str(e)}")
        return False

def test_symbol_switching():
    """测试交易对切换"""
    print("\n" + "=" * 60)
    print("测试交易对切换")
    print("=" * 60)
    
    try:
        manager = get_symbol_manager()
        available_symbols = manager.get_available_symbols()
        
        if len(available_symbols) < 2:
            print("⚠️ 可用交易对少于2个，跳过切换测试")
            return True
        
        original_symbol = get_current_symbol()
        print(f"原始交易对: {original_symbol}")
        
        # 找一个不同的交易对进行切换
        target_symbol = None
        for symbol in available_symbols:
            if symbol != original_symbol:
                target_symbol = symbol
                break
        
        if not target_symbol:
            print("⚠️ 没有找到可切换的交易对")
            return True
        
        # 切换交易对
        print(f"切换到: {target_symbol}")
        if set_current_symbol(target_symbol):
            new_symbol = get_current_symbol()
            new_base = get_base_currency()
            new_quote = get_quote_currency()
            
            print(f"✅ 切换成功: {new_symbol} ({new_base}/{new_quote})")
            
            # 切换回原来的交易对
            print(f"切换回: {original_symbol}")
            if set_current_symbol(original_symbol):
                restored_symbol = get_current_symbol()
                print(f"✅ 恢复成功: {restored_symbol}")
                return True
            else:
                print(f"❌ 恢复失败")
                return False
        else:
            print(f"❌ 切换失败")
            return False
            
    except Exception as e:
        print(f"❌ 交易对切换测试失败: {str(e)}")
        return False

def test_algorithm_integration():
    """测试与算法模块的集成"""
    print("\n" + "=" * 60)
    print("测试算法模块集成")
    print("=" * 60)
    
    try:
        from byb_order_algorithm import BYBOrderAlgorithm, get_trading_config
        
        # 测试交易配置获取
        trading_config = get_trading_config()
        print(f"✅ 交易配置获取成功:")
        print(f"   交易对: {trading_config['symbol']}")
        print(f"   最小订单量: {trading_config['min_order_size']}")
        print(f"   最大订单量: {trading_config['max_order_size']}")
        
        # 测试算法实例化
        algorithm = BYBOrderAlgorithm()
        print(f"✅ 算法实例化成功")
        
        # 测试价格获取（可能会失败，但不影响测试）
        try:
            current_price = algorithm.get_current_price()
            print(f"✅ 价格获取成功: {current_price}")
        except Exception as e:
            print(f"⚠️ 价格获取失败（正常，可能是网络问题）: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法模块集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_buy_back_integration():
    """测试与回购系统的集成"""
    print("\n" + "=" * 60)
    print("测试回购系统集成")
    print("=" * 60)
    
    try:
        from byb_buy_back import get_monitor_config
        
        # 测试监控配置获取
        monitor_config = get_monitor_config()
        print(f"✅ 监控配置获取成功:")
        print(f"   交易对: {monitor_config['symbol']}")
        print(f"   基础货币: {monitor_config['base_currency']}")
        print(f"   计价货币: {monitor_config['quote_currency']}")
        print(f"   检查间隔: {monitor_config['check_interval']} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 回购系统集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_symbol_scenario():
    """测试多交易对场景"""
    print("\n" + "=" * 60)
    print("测试多交易对场景")
    print("=" * 60)
    
    try:
        manager = get_symbol_manager()
        available_symbols = manager.get_available_symbols()
        
        print(f"可用交易对数量: {len(available_symbols)}")
        
        # 测试每个交易对的配置
        for symbol in available_symbols:
            print(f"\n测试交易对: {symbol}")
            
            if set_current_symbol(symbol):
                config = get_current_config()
                base = get_base_currency()
                quote = get_quote_currency()
                
                print(f"  ✅ {symbol}: {base}/{quote}")
                print(f"     订单量范围: {config.min_order_size} - {config.max_order_size}")
                print(f"     默认价格: {config.default_price}")
                
                # 测试交易配置
                try:
                    from byb_order_algorithm import get_trading_config
                    trading_config = get_trading_config()
                    print(f"     交易配置: {trading_config['symbol']}")
                except Exception as e:
                    print(f"     ⚠️ 交易配置获取失败: {str(e)}")
            else:
                print(f"  ❌ 切换到 {symbol} 失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 多交易对场景测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("全局交易对配置系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("基本功能", test_basic_functionality()))
    test_results.append(("交易对切换", test_symbol_switching()))
    test_results.append(("算法模块集成", test_algorithm_integration()))
    test_results.append(("回购系统集成", test_buy_back_integration()))
    test_results.append(("多交易对场景", test_multi_symbol_scenario()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！全局交易对配置系统工作正常")
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
    
    print("\n使用说明:")
    print("1. 使用 python symbol_manager.py 管理交易对配置")
    print("2. 使用 python symbol_manager.py switch <symbol> 快速切换交易对")
    print("3. 修改后的回购系统会自动使用当前配置的交易对")

if __name__ == "__main__":
    main()
