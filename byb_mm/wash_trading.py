import sys
sys.path.append("/home/<USER>/byb_mm/")
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import random
import time
import pandas as pd
import numpy as np
import config
import con_pri
import logging
import requests
import pytz
from datetime import datetime
import asyncio
import json
import os


beijing_tz = pytz.timezone('Asia/Shanghai')

spot_market = Spot()
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)

# 记录上次检查交易的时间
last_trade_check_time = time.time()

# 共享价格文件路径
SHARED_PRICES_FILE = 'current_mm_prices.json'


def send_telegram_message(text):
    bot_token = con_pri.bot_token
    chat_id = con_pri.chat_id

    message_parts = []
    message_parts.extend([
        f"{text}",
        # f"information2: {variable 2}"
    ])

    message = "\n".join(message_parts)

    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    params = {"chat_id": chat_id, "text": message}

    try:
        response = requests.post(url, params=params)
        response.raise_for_status()
    except Exception as e:
        logging.error(f"Failed to send Telegram message: {e} {message}")


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        dt = datetime.fromtimestamp(timestamp, beijing_tz)
        return dt.timetuple()

logging.basicConfig(
    filename='wash_trading.log',  # Log to this file
    level=logging.DEBUG,  # Set log level to DEBUG to capture all log messages
    format='%(asctime)s - %(levelname)s - %(message)s',  # Log format
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))


def get_current_mm_prices():
    """
    获取当前做市商生成的ask和bid价格
    Returns:
        tuple: (bid_price, ask_price) 或 (None, None) 如果获取失败
    """
    try:
        # 尝试从共享文件读取价格
        if os.path.exists(SHARED_PRICES_FILE):
            with open(SHARED_PRICES_FILE, 'r') as f:
                price_data = json.load(f)
                bid_price = price_data.get('bid_price')
                ask_price = price_data.get('ask_price')
                timestamp = price_data.get('timestamp', 0)

                # 检查价格数据是否过期（超过10秒）
                if time.time() - timestamp < 10:
                    logging.debug(f"从共享文件获取做市商价格: bid={bid_price}, ask={ask_price}")
                    return bid_price, ask_price
                else:
                    logging.warning("共享价格文件数据过期")

        # 如果文件不存在或数据过期，尝试从订单簿获取当前最优价格
        df_asks, df_bids = spot_market.get_orderbook("bybusdt")
        if not df_asks.empty and not df_bids.empty:
            current_bid = float(df_bids['bids_price'].iloc[0])
            current_ask = float(df_asks['asks_price'].iloc[0])
            logging.debug(f"从订单簿获取当前价格: bid={current_bid}, ask={current_ask}")
            return current_bid, current_ask

    except Exception as e:
        logging.error(f"获取做市商价格失败: {e}")

    return None, None


def save_current_mm_prices(bid_price, ask_price):
    """
    保存当前做市商价格到共享文件
    Args:
        bid_price: 买一价格
        ask_price: 卖一价格
    """
    try:
        price_data = {
            'bid_price': bid_price,
            'ask_price': ask_price,
            'timestamp': time.time()
        }
        with open(SHARED_PRICES_FILE, 'w') as f:
            json.dump(price_data, f)
        logging.debug(f"保存做市商价格: bid={bid_price}, ask={ask_price}")
    except Exception as e:
        logging.error(f"保存做市商价格失败: {e}")


def check_recent_trades(symbol="bybusdt", no_trade_threshold=60):
    """
    检查最近是否有交易活动
    Args:
        symbol: 交易对
        no_trade_threshold: 无交易时间阈值（秒）
    Returns:
        bool: True表示最近没有交易（需要刷量），False表示有交易
    """
    try:
        df_trades = spot_market.get_trades(symbol=symbol)
        if df_trades.empty:
            logging.info("无法获取成交记录，默认需要刷量")
            return True

        # 获取最新交易时间（ctime字段是毫秒时间戳）
        latest_trade_time = df_trades['ctime'].max() / 1000  # 转换为秒
        current_time = time.time()
        time_since_last_trade = current_time - latest_trade_time

        # 友好显示时间
        if time_since_last_trade >= 60:
            minutes = time_since_last_trade / 60
            logging.info(f"距离最后一笔交易已过去 {minutes:.1f} 分钟 ({time_since_last_trade:.1f} 秒)")
        else:
            logging.info(f"距离最后一笔交易已过去 {time_since_last_trade:.1f} 秒")

        # 如果距离最后交易超过阈值，则需要刷量
        return time_since_last_trade > no_trade_threshold

    except Exception as e:
        logging.error(f"检查交易记录失败: {e}")
        return True  # 出错时默认需要刷量


def calculate_wash_qty_from_orderbook(symbol="bybusdt", order_side='buy'):
    """
    根据订单簿1-3档总量计算刷量数量
    Returns:
        float: 刷量数量（1-3档总量的10%-80%，但有合理上限）
    """
    try:
        df_asks, df_bids = spot_market.get_orderbook(symbol)

        if df_asks.empty or df_bids.empty:
            logging.warning("订单簿为空，使用默认刷量数量")
            return int(random.uniform(50, 200))

        # 计算1-3档总量
        top_3_ask_qty = df_asks['asks_qty'].head(3).astype(float).sum()
        top_3_bid_qty = df_bids['bids_qty'].head(3).astype(float).sum()
        if order_side == "buy":
            total_top_3_qty = top_3_ask_qty
        else:
            total_top_3_qty = top_3_bid_qty

        # 随机选择10%-80%的比例
        ratio = random.uniform(0.1, 0.8)
        wash_qty = total_top_3_qty * ratio

        # 设置合理的上下限，避免数量过大或过小
        min_qty = 50.0   # 最小刷量数量
        max_qty = 2000.0  # 最大刷量数量，避免超过交易所限制

        wash_qty = max(min_qty, min(wash_qty, max_qty))

        # 由于API需要整数，所以转换为整数
        wash_qty = int(wash_qty)

        logging.info(f"1-3档总量: {total_top_3_qty:.2f}, 刷量比例: {ratio:.1%}, 刷量数量: {wash_qty}")

        return wash_qty

    except Exception as e:
        logging.error(f"计算刷量数量失败: {e}")
        # 出错时返回随机数量
        return int(random.uniform(50, 200))


def generate_random_side():
    """
    随机生成交易方向（完全随机）
    Returns:
        str: "buy" 或 "sell"
    """
    return random.choice(["buy", "sell"])


def generate_random_sleep(random_lower: float=30, random_upper: float=60):
    """
    在范围内随机生成睡眠时间，控制检查频率
    Args:
        random_lower: 最小睡眠时间（秒）
        random_upper: 最大睡眠时间（秒）
    Returns:
        float: 睡眠时间
    """
    return round(random.uniform(random_lower, random_upper), 1)


def main():
    """
    主刷量循环：检测交易活动，在无交易时进行刷量
    """
    symbol = 'bybusdt'
    logging.info("刷量程序启动，开始监控交易活动...")
    logging.info(f"监控交易对: {symbol}")
    logging.info("刷量策略: 检测1-4分钟无交易时进行刷量，数量为1-3档总量的10%-80%，买卖方向随机")
    logging.info("价格联动: 与byb_mm.py生成的ask/bid档位联动，只在做市商档位上刷量")

    while True:
        try:
            # 随机等待30-60秒后检查交易活动
            sleep_time = generate_random_sleep()
            time.sleep(sleep_time)

            # # 检查最近是否有交易活动
            # no_trade_threshold = random.uniform(30, 120)  # 随机1-2分钟阈值（30-120秒）
            # need_wash_trade = check_recent_trades(symbol, no_trade_threshold)

            # if not need_wash_trade:
            #     logging.info("最近有交易活动，暂不刷量")
            #     continue

            # 获取当前做市商生成的ask和bid价格
            current_bid, current_ask = get_current_mm_prices()

            if current_bid is None or current_ask is None:
                logging.warning("无法获取做市商价格，跳过本次刷量")
                continue

            p_buy = current_bid
            p_sell = current_ask

            # 获取订单簿数据
            df_asks, df_bids = spot_market.get_orderbook(symbol)
            if df_asks.empty or df_bids.empty:
                logging.warning("订单簿为空，跳过本次刷量")
                continue

            # 随机选择交易方向
            order_side = generate_random_side()

            # 根据订单簿计算刷量数量
            order_qty = calculate_wash_qty_from_orderbook(symbol, order_side)

            # 确定交易价格和数量
            if order_side == "buy":
                # 买单：检查卖一价格是否为用户铺单
                current_ask_price = float(df_asks['asks_price'].iloc[0])
                if abs(current_ask_price - p_sell) < 0.0001:  # 使用浮点数比较
                    order_price = current_ask_price
                    # 限制订单量不超过买一档位的50%，更保守
                    max_qty = max(int(float(df_bids['bids_qty'].iloc[0]) * 0.5), int(500/order_price))
                    order_qty = min(order_qty, max_qty)

                    # 确保数量不小于最小值
                    if order_qty < 10:
                        logging.info(f"计算的刷量数量过小({order_qty})，跳过本次刷量")
                        continue
                else:
                    logging.info(f"卖一档价格({current_ask_price})不是用户铺单价格({p_sell})，跳过买单刷量")
                    continue
            else:
                # 卖单：检查买一价格是否为用户铺单
                current_bid_price = float(df_bids['bids_price'].iloc[0])
                if abs(current_bid_price - p_buy) < 0.0001:  # 使用浮点数比较
                    order_price = current_bid_price
                    # 限制订单量不超过买一档位的50%，更保守
                    max_qty = max(int(float(df_bids['bids_qty'].iloc[0]) * 0.5), int(500/order_price))
                    order_qty = min(order_qty, max_qty)

                    # 确保数量不小于最小值
                    if order_qty < 10:
                        logging.info(f"计算的刷量数量过小({order_qty})，跳过本次刷量")
                        continue
                else:
                    logging.info(f"买一档价格({current_bid_price})不是用户铺单价格({p_buy})，跳过卖单刷量")
                    continue

            # 最终数量和价格处理
            order_qty = int(order_qty)  # 确保是整数
            order_price = round(order_price, 4)  # 价格保留4位小数

            # 执行刷量交易
            logging.info(f"准备执行刷量: 方向={order_side}, 数量={order_qty}, 价格={order_price:.4f}")
            logging.info(f"做市商价格: bid={p_buy:.4f}, ask={p_sell:.4f}")

            try:
                # 验证订单参数
                if order_qty <= 0:
                    logging.error(f"订单数量无效: {order_qty}")
                    continue

                if order_price <= 0:
                    logging.error(f"订单价格无效: {order_price}")
                    continue

                # # 生成客户端订单ID
                # client_order_id = f"wash_{int(time.time() * 1000)}"

                # 执行自成交 - volume需要是int类型
                order = spot_client.self_trade(
                    symbol=symbol,
                    side=order_side,
                    volume=order_qty,  # 已经是整数
                    type=1,
                    price=str(order_price),
                    # clientOrderId=client_order_id
                )

                if order and 'code' in order:
                    if order['code'] == '0':
                        logging.info(f"刷量订单执行成功: {order}")
                    else:
                        logging.error(f"刷量订单执行失败: {order.get('msg', '未知错误')}")
                else:
                    logging.info(f"刷量订单提交: {order}")

            except Exception as e:
                logging.error(f"刷量订单执行异常: {e}")
                # 如果是API错误，等待更长时间再继续
                if "API" in str(e) or "failed" in str(e).lower():
                    logging.info("检测到API错误，等待30秒后继续...")
                    time.sleep(30)
                continue

        except Exception as e:
            logging.error(f"刷量循环出现错误: {e}")
            # 出错时等待一段时间再继续
            time.sleep(10)


if __name__ == '__main__':
    main()